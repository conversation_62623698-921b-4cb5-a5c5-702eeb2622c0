<?php

namespace App\Traits;

trait ModelActionTrait
{
    public function edit($route)
    {
        return ' <li><a href="' . $route . '" style="cursor: pointer" class="bs-tooltip" data-toggle="tooltip" data-placement="top" title="Edit" data-original-title="Edit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit-2 p-1 br-6 mb-1"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg></a></li>';
    }

    public function view($route)
    {
        return '<li> <a href="' . $route . '"   class="bs-tooltip"  title="View"><svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-eye"><path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"></path><circle cx="12" cy="12" r="3"></circle></svg></a></li>';
    }

    public function editModel($query)
    {
        return '<li><a onclick="editModal(`' . $query->uuid . '`)" style="cursor: pointer" class="bs-tooltip" data-toggle="tooltip" data-placement="top" title="Edit" data-original-title="Edit"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-edit-2 p-1 br-6 mb-1"><path d="M17 3a2.828 2.828 0 1 1 4 4L7.5 20.5 2 22l1.5-5.5L17 3z"></path></svg></a></li>';
    }

    public function deleteModel($query)
    {
        return '<li><a href="#" onclick="deleteUser(' . $query->id . ')" style="cursor: pointer" title="Delete" class="" id="deleted"><svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="feather feather-trash p-1 br-6 mb-1"><polyline points="3 6 5 6 21 6"></polyline><path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2"></path></svg></a></li>';
    }

    public function loginAsSupplier($query)
    {
        return '<li><a onclick="loginAsSupplier(' . $query->id . ')" style="cursor: pointer" title="Impersonate User" class="bs-tooltip"><i class="las la-clock" style="font-size: 19px;padding-top: 4px;"></i></a></li>';
    }
}
