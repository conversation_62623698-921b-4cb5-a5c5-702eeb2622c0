<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * PremiceSoftComplete Model
 * 
 * This model stores completed transaction details from the Premice payment gateway.
 * It provides detailed information for accounting and reconciliation.
 */
class PremiceSoftComplete extends Model
{
    use HasFactory;

    /**
     * The table associated with the model.
     *
     * @var string
     */
    protected $table = '__premice_soft_complete';

    /**
     * The primary key for the model.
     *
     * @var string
     */
    protected $primaryKey = 'Transaction';

    /**
     * The primary key type is string.
     *
     * @var string
     */
    protected $keyType = 'string';

    /**
     * Indicates if the IDs are auto-incrementing.
     *
     * @var bool
     */
    public $incrementing = false;

    /**
     * Indicates if the model should be timestamped.
     *
     * @var bool
     */
    public $timestamps = false;

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'Transaction',
        'Creator',
        'Customer',
        'Email',
        'Phone',
        'Currency',
        'Transaction Type',
        'Amount',
        'Processing Fee',
        'Amount Received',
        'Date',
        'Method',
        'Notes',
        'Invoice Number',
        'PayLink Label',
        'Auth Code',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'Date' => 'datetime',
        'Amount' => 'float',
        'Processing Fee' => 'float',
        'Amount Received' => 'float',
    ];

    /**
     * Get the Premice payment record associated with this completed transaction.
     */
    public function premicePayment()
    {
        return $this->belongsTo(PremiceSoft::class, 'Invoice Number', 'invoice_id');
    }

    /**
     * Get the member associated with this payment based on invoice number.
     */
    public function member()
    {
        if ($this->premicePayment) {
            return $this->premicePayment->member();
        }
        
        // Try to parse the invoice ID if not linked to a premice payment
        $invoiceId = $this->getAttribute('Invoice Number');
        if (empty($invoiceId)) {
            return null;
        }
        
        $parts = explode('-', $invoiceId);
        if (count($parts) !== 3) {
            return null;
        }
        
        $cardId = $parts[1];
        return Member::where('card_id', $cardId)->first();
    }

    /**
     * Determine if this is an ambassador level purchase.
     *
     * @return bool
     */
    public function isAmbassadorPurchase()
    {
        $invoiceId = $this->getAttribute('Invoice Number');
        if (empty($invoiceId)) {
            return false;
        }
        
        $parts = explode('-', $invoiceId);
        if (count($parts) !== 3) {
            return false;
        }
        
        $type = $parts[0];
        return preg_match('/^AMB[1-7]$/', $type) === 1;
    }

    /**
     * Get the ambassador level from the invoice number.
     *
     * @return int|null
     */
    public function getAmbassadorLevel()
    {
        if (!$this->isAmbassadorPurchase()) {
            return null;
        }
        
        $invoiceId = $this->getAttribute('Invoice Number');
        $parts = explode('-', $invoiceId);
        $type = $parts[0];
        
        return (int) substr($type, 3, 1);
    }
} 