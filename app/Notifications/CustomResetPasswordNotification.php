<?php

namespace App\Notifications;

use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Auth\Notifications\ResetPassword as ResetPasswordNotification;
use Illuminate\Support\Facades\URL;

final class CustomResetPasswordNotification extends ResetPasswordNotification implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new notification instance.
     *
     * @param  string  $token
     * @return void
     */
    public function __construct(string $token)
    {
        parent::__construct($token);
    }

    /**
     * Get the mail representation of the notification.
     *
     * @param  mixed  $notifiable
     * @return \Illuminate\Notifications\Messages\MailMessage
     */
    public function toMail(mixed $notifiable): MailMessage
    {
        $url = URL::signedRoute('password.reset', [
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ]);

        dd($url);
        return (new MailMessage)
            ->subject('Important: Password Reset Required for Des Alpes Community Platform')
            ->greeting('Hello ' . $notifiable->name . ',')
            ->line('To continue using our new platform securely, all members are required to create a new, stronger password. This transition is part of our ongoing effort to protect your data and our community.')
            ->action('Reset Password', $url)
            ->line('Only emails from @desalpes.world are legitimate. If you receive messages from any other address, do not click any links and report them immediately.')
            ->salutation('Best regards, The ' . config('app.name') . ' Team');
    }

    /**
     * Get the array representation of the notification.
     *
     * @return array<string, mixed>
     */
    public function toArray(object $notifiable): array
    {
        return [
            'token' => $this->token,
            'notifiable_id' => $notifiable->id,
            'notifiable_type' => $notifiable::class
        ];
    }
}
