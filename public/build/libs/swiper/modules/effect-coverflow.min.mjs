import{c as createShadow}from"../shared/create-shadow.min.mjs";import{e as effectInit}from"../shared/effect-init.min.mjs";import{e as effectTarget}from"../shared/effect-target.min.mjs";import{g as getSlideTransformEl,o as getRotateFix}from"../shared/utils.min.mjs";function EffectCoverflow(e){let{swiper:t,extendParams:s,on:r}=e;s({coverflowEffect:{rotate:50,stretch:0,depth:100,scale:1,modifier:1,slideShadows:!0}});effectInit({effect:"coverflow",swiper:t,on:r,setTranslate:()=>{const{width:e,height:s,slides:r,slidesSizesGrid:o}=t,a=t.params.coverflowEffect,i=t.isHorizontal(),l=t.translate,f=i?e/2-l:s/2-l,d=i?a.rotate:-a.rotate,c=a.depth,h=getRotateFix(t);for(let e=0,t=r.length;e<t;e+=1){const t=r[e],s=o[e],l=(f-t.swiperSlideOffset-s/2)/s,n="function"==typeof a.modifier?a.modifier(l):l*a.modifier;let w=i?d*n:0,p=i?0:d*n,m=-c*Math.abs(n),g=a.stretch;"string"==typeof g&&-1!==g.indexOf("%")&&(g=parseFloat(a.stretch)/100*s);let y=i?0:g*n,S=i?g*n:0,b=1-(1-a.scale)*Math.abs(n);Math.abs(S)<.001&&(S=0),Math.abs(y)<.001&&(y=0),Math.abs(m)<.001&&(m=0),Math.abs(w)<.001&&(w=0),Math.abs(p)<.001&&(p=0),Math.abs(b)<.001&&(b=0);const u=`translate3d(${S}px,${y}px,${m}px)  rotateX(${h(p)}deg) rotateY(${h(w)}deg) scale(${b})`;if(effectTarget(a,t).style.transform=u,t.style.zIndex=1-Math.abs(Math.round(n)),a.slideShadows){let e=i?t.querySelector(".swiper-slide-shadow-left"):t.querySelector(".swiper-slide-shadow-top"),s=i?t.querySelector(".swiper-slide-shadow-right"):t.querySelector(".swiper-slide-shadow-bottom");e||(e=createShadow("coverflow",t,i?"left":"top")),s||(s=createShadow("coverflow",t,i?"right":"bottom")),e&&(e.style.opacity=n>0?n:0),s&&(s.style.opacity=-n>0?-n:0)}}},setTransition:e=>{t.slides.map((e=>getSlideTransformEl(e))).forEach((t=>{t.style.transitionDuration=`${e}ms`,t.querySelectorAll(".swiper-slide-shadow-top, .swiper-slide-shadow-right, .swiper-slide-shadow-bottom, .swiper-slide-shadow-left").forEach((t=>{t.style.transitionDuration=`${e}ms`}))}))},perspective:()=>!0,overwriteParams:()=>({watchSlidesProgress:!0})})}export{EffectCoverflow as default};
//# sourceMappingURL=effect-coverflow.min.mjs.map