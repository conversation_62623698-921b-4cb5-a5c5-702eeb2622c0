import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{s as setCSSProperty,e as elementChildren,c as createElement}from"../shared/utils.min.mjs";function Virtual(e){let s,{swiper:t,extendParams:i,on:r,emit:a}=e;i({virtual:{enabled:!1,slides:[],cache:!0,renderSlide:null,renderExternal:null,renderExternalUpdate:!0,addSlidesBefore:0,addSlidesAfter:0}});const l=getDocument();t.virtual={cache:{},from:void 0,to:void 0,slides:[],offset:0,slidesGrid:[]};const d=l.createElement("div");function n(e,s){const i=t.params.virtual;if(i.cache&&t.virtual.cache[s])return t.virtual.cache[s];let r;return i.renderSlide?(r=i.renderSlide.call(t,e,s),"string"==typeof r&&(d.innerHTML=r,r=d.children[0])):r=t.isElement?createElement("swiper-slide"):createElement("div",t.params.slideClass),r.setAttribute("data-swiper-slide-index",s),i.renderSlide||(r.innerHTML=e),i.cache&&(t.virtual.cache[s]=r),r}function c(e,s){const{slidesPerView:i,slidesPerGroup:r,centeredSlides:l,loop:d,initialSlide:c}=t.params;if(s&&!d&&c>0)return;const{addSlidesBefore:o,addSlidesAfter:u}=t.params.virtual,{from:p,to:h,slides:f,slidesGrid:v,offset:m}=t.virtual;t.params.cssMode||t.updateActiveIndex();const g=t.activeIndex||0;let E,x,w;E=t.rtlTranslate?"right":t.isHorizontal()?"left":"top",l?(x=Math.floor(i/2)+r+u,w=Math.floor(i/2)+r+o):(x=i+(r-1)+u,w=(d?i:r)+o);let S=g-w,b=g+x;d||(S=Math.max(S,0),b=Math.min(b,f.length-1));let A=(t.slidesGrid[S]||0)-(t.slidesGrid[0]||0);function M(){t.updateSlides(),t.updateProgress(),t.updateSlidesClasses(),a("virtualUpdate")}if(d&&g>=w?(S-=w,l||(A+=t.slidesGrid[0])):d&&g<w&&(S=-w,l&&(A+=t.slidesGrid[0])),Object.assign(t.virtual,{from:S,to:b,offset:A,slidesGrid:t.slidesGrid,slidesBefore:w,slidesAfter:x}),p===S&&h===b&&!e)return t.slidesGrid!==v&&A!==m&&t.slides.forEach((e=>{e.style[E]=A-Math.abs(t.cssOverflowAdjustment())+"px"})),t.updateProgress(),void a("virtualUpdate");if(t.params.virtual.renderExternal)return t.params.virtual.renderExternal.call(t,{offset:A,from:S,to:b,slides:function(){const e=[];for(let s=S;s<=b;s+=1)e.push(f[s]);return e}()}),void(t.params.virtual.renderExternalUpdate?M():a("virtualUpdate"));const y=[],P=[],j=e=>{let s=e;return e<0?s=f.length+e:s>=f.length&&(s-=f.length),s};if(e)t.slides.filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`))).forEach((e=>{e.remove()}));else for(let e=p;e<=h;e+=1)if(e<S||e>b){const s=j(e);t.slides.filter((e=>e.matches(`.${t.params.slideClass}[data-swiper-slide-index="${s}"], swiper-slide[data-swiper-slide-index="${s}"]`))).forEach((e=>{e.remove()}))}const C=d?-f.length:0,G=d?2*f.length:f.length;for(let s=C;s<G;s+=1)if(s>=S&&s<=b){const t=j(s);void 0===h||e?P.push(t):(s>h&&P.push(t),s<p&&y.push(t))}if(P.forEach((e=>{t.slidesEl.append(n(f[e],e))})),d)for(let e=y.length-1;e>=0;e-=1){const s=y[e];t.slidesEl.prepend(n(f[s],s))}else y.sort(((e,s)=>s-e)),y.forEach((e=>{t.slidesEl.prepend(n(f[e],e))}));elementChildren(t.slidesEl,".swiper-slide, swiper-slide").forEach((e=>{e.style[E]=A-Math.abs(t.cssOverflowAdjustment())+"px"})),M()}r("beforeInit",(()=>{if(!t.params.virtual.enabled)return;let e;if(void 0===t.passedParams.virtual.slides){const s=[...t.slidesEl.children].filter((e=>e.matches(`.${t.params.slideClass}, swiper-slide`)));s&&s.length&&(t.virtual.slides=[...s],e=!0,s.forEach(((e,s)=>{e.setAttribute("data-swiper-slide-index",s),t.virtual.cache[s]=e,e.remove()})))}e||(t.virtual.slides=t.params.virtual.slides),t.classNames.push(`${t.params.containerModifierClass}virtual`),t.params.watchSlidesProgress=!0,t.originalParams.watchSlidesProgress=!0,c(!1,!0)})),r("setTranslate",(()=>{t.params.virtual.enabled&&(t.params.cssMode&&!t._immediateVirtual?(clearTimeout(s),s=setTimeout((()=>{c()}),100)):c())})),r("init update resize",(()=>{t.params.virtual.enabled&&t.params.cssMode&&setCSSProperty(t.wrapperEl,"--swiper-virtual-size",`${t.virtualSize}px`)})),Object.assign(t.virtual,{appendSlide:function(e){if("object"==typeof e&&"length"in e)for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.push(e[s]);else t.virtual.slides.push(e);c(!0)},prependSlide:function(e){const s=t.activeIndex;let i=s+1,r=1;if(Array.isArray(e)){for(let s=0;s<e.length;s+=1)e[s]&&t.virtual.slides.unshift(e[s]);i=s+e.length,r=e.length}else t.virtual.slides.unshift(e);if(t.params.virtual.cache){const e=t.virtual.cache,s={};Object.keys(e).forEach((t=>{const i=e[t],a=i.getAttribute("data-swiper-slide-index");a&&i.setAttribute("data-swiper-slide-index",parseInt(a,10)+r),s[parseInt(t,10)+r]=i})),t.virtual.cache=s}c(!0),t.slideTo(i,0)},removeSlide:function(e){if(null==e)return;let s=t.activeIndex;if(Array.isArray(e))for(let i=e.length-1;i>=0;i-=1)t.params.virtual.cache&&(delete t.virtual.cache[e[i]],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e[i],1),e[i]<s&&(s-=1),s=Math.max(s,0);else t.params.virtual.cache&&(delete t.virtual.cache[e],Object.keys(t.virtual.cache).forEach((s=>{s>e&&(t.virtual.cache[s-1]=t.virtual.cache[s],t.virtual.cache[s-1].setAttribute("data-swiper-slide-index",s-1),delete t.virtual.cache[s])}))),t.virtual.slides.splice(e,1),e<s&&(s-=1),s=Math.max(s,0);c(!0),t.slideTo(s,0)},removeAllSlides:function(){t.virtual.slides=[],t.params.virtual.cache&&(t.virtual.cache={}),c(!0),t.slideTo(0,0)},update:c})}export{Virtual as default};
//# sourceMappingURL=virtual.min.mjs.map