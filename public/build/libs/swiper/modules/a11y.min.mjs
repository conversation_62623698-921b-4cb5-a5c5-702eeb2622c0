import{g as getDocument}from"../shared/ssr-window.esm.min.mjs";import{c as classesToSelector}from"../shared/classes-to-selector.min.mjs";import{c as createElement,h as elementIndex,m as makeElementsArray}from"../shared/utils.min.mjs";function A11y(e){let{swiper:a,extendParams:t,on:n}=e;t({a11y:{enabled:!0,notificationClass:"swiper-notification",prevSlideMessage:"Previous slide",nextSlideMessage:"Next slide",firstSlideMessage:"This is the first slide",lastSlideMessage:"This is the last slide",paginationBulletMessage:"Go to slide {{index}}",slideLabelMessage:"{{index}} / {{slidesLength}}",containerMessage:null,containerRoleDescriptionMessage:null,containerRole:null,itemRoleDescriptionMessage:null,slideRole:"group",id:null,scrollOnFocus:!0}}),a.a11y={clicked:!1};let i,s,r=null,l=(new Date).getTime();function o(e){const a=r;0!==a.length&&(a.innerHTML="",a.innerHTML=e)}function c(e){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("tabIndex","0")}))}function d(e){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("tabIndex","-1")}))}function m(e,a){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("role",a)}))}function p(e,a){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-roledescription",a)}))}function g(e,a){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-label",a)}))}function u(e){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-disabled",!0)}))}function E(e){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-disabled",!1)}))}function f(e){if(13!==e.keyCode&&32!==e.keyCode)return;const t=a.params.a11y,n=e.target;if(!a.pagination||!a.pagination.el||n!==a.pagination.el&&!a.pagination.el.contains(e.target)||e.target.matches(classesToSelector(a.params.pagination.bulletClass))){if(a.navigation&&a.navigation.prevEl&&a.navigation.nextEl){const e=makeElementsArray(a.navigation.prevEl);makeElementsArray(a.navigation.nextEl).includes(n)&&(a.isEnd&&!a.params.loop||a.slideNext(),a.isEnd?o(t.lastSlideMessage):o(t.nextSlideMessage)),e.includes(n)&&(a.isBeginning&&!a.params.loop||a.slidePrev(),a.isBeginning?o(t.firstSlideMessage):o(t.prevSlideMessage))}a.pagination&&n.matches(classesToSelector(a.params.pagination.bulletClass))&&n.click()}}function v(){return a.pagination&&a.pagination.bullets&&a.pagination.bullets.length}function y(){return v()&&a.params.pagination.clickable}const b=(e,a,t)=>{c(e),"BUTTON"!==e.tagName&&(m(e,"button"),e.addEventListener("keydown",f)),g(e,t),function(e,a){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-controls",a)}))}(e,a)},h=e=>{s&&s!==e.target&&!s.contains(e.target)&&(i=!0),a.a11y.clicked=!0},A=()=>{i=!1,requestAnimationFrame((()=>{requestAnimationFrame((()=>{a.destroyed||(a.a11y.clicked=!1)}))}))},k=e=>{l=(new Date).getTime()},M=e=>{if(a.a11y.clicked||!a.params.a11y.scrollOnFocus)return;if((new Date).getTime()-l<100)return;const t=e.target.closest(`.${a.params.slideClass}, swiper-slide`);if(!t||!a.slides.includes(t))return;s=t;const n=a.slides.indexOf(t)===a.activeIndex,r=a.params.watchSlidesProgress&&a.visibleSlides&&a.visibleSlides.includes(t);n||r||e.sourceCapabilities&&e.sourceCapabilities.firesTouchEvents||(a.isHorizontal()?a.el.scrollLeft=0:a.el.scrollTop=0,requestAnimationFrame((()=>{i||(a.params.loop?a.slideToLoop(parseInt(t.getAttribute("data-swiper-slide-index")),0):a.slideTo(a.slides.indexOf(t),0),i=!1)})))},x=()=>{const e=a.params.a11y;e.itemRoleDescriptionMessage&&p(a.slides,e.itemRoleDescriptionMessage),e.slideRole&&m(a.slides,e.slideRole);const t=a.slides.length;e.slideLabelMessage&&a.slides.forEach(((n,i)=>{const s=a.params.loop?parseInt(n.getAttribute("data-swiper-slide-index"),10):i;g(n,e.slideLabelMessage.replace(/\{\{index\}\}/,s+1).replace(/\{\{slidesLength\}\}/,t))}))},L=()=>{const e=a.params.a11y;a.el.append(r);const t=a.el;e.containerRoleDescriptionMessage&&p(t,e.containerRoleDescriptionMessage),e.containerMessage&&g(t,e.containerMessage),e.containerRole&&m(t,e.containerRole);const n=a.wrapperEl,i=e.id||n.getAttribute("id")||`swiper-wrapper-${s=16,void 0===s&&(s=16),"x".repeat(s).replace(/x/g,(()=>Math.round(16*Math.random()).toString(16)))}`;var s;const l=a.params.autoplay&&a.params.autoplay.enabled?"off":"polite";var o;o=i,makeElementsArray(n).forEach((e=>{e.setAttribute("id",o)})),function(e,a){(e=makeElementsArray(e)).forEach((e=>{e.setAttribute("aria-live",a)}))}(n,l),x();let{nextEl:c,prevEl:d}=a.navigation?a.navigation:{};if(c=makeElementsArray(c),d=makeElementsArray(d),c&&c.forEach((a=>b(a,i,e.nextSlideMessage))),d&&d.forEach((a=>b(a,i,e.prevSlideMessage))),y()){makeElementsArray(a.pagination.el).forEach((e=>{e.addEventListener("keydown",f)}))}getDocument().addEventListener("visibilitychange",k),a.el.addEventListener("focus",M,!0),a.el.addEventListener("focus",M,!0),a.el.addEventListener("pointerdown",h,!0),a.el.addEventListener("pointerup",A,!0)};n("beforeInit",(()=>{r=createElement("span",a.params.a11y.notificationClass),r.setAttribute("aria-live","assertive"),r.setAttribute("aria-atomic","true")})),n("afterInit",(()=>{a.params.a11y.enabled&&L()})),n("slidesLengthChange snapGridLengthChange slidesGridLengthChange",(()=>{a.params.a11y.enabled&&x()})),n("fromEdge toEdge afterInit lock unlock",(()=>{a.params.a11y.enabled&&function(){if(a.params.loop||a.params.rewind||!a.navigation)return;const{nextEl:e,prevEl:t}=a.navigation;t&&(a.isBeginning?(u(t),d(t)):(E(t),c(t))),e&&(a.isEnd?(u(e),d(e)):(E(e),c(e)))}()})),n("paginationUpdate",(()=>{a.params.a11y.enabled&&function(){const e=a.params.a11y;v()&&a.pagination.bullets.forEach((t=>{a.params.pagination.clickable&&(c(t),a.params.pagination.renderBullet||(m(t,"button"),g(t,e.paginationBulletMessage.replace(/\{\{index\}\}/,elementIndex(t)+1)))),t.matches(classesToSelector(a.params.pagination.bulletActiveClass))?t.setAttribute("aria-current","true"):t.removeAttribute("aria-current")}))}()})),n("destroy",(()=>{a.params.a11y.enabled&&function(){r&&r.remove();let{nextEl:e,prevEl:t}=a.navigation?a.navigation:{};e=makeElementsArray(e),t=makeElementsArray(t),e&&e.forEach((e=>e.removeEventListener("keydown",f))),t&&t.forEach((e=>e.removeEventListener("keydown",f))),y()&&makeElementsArray(a.pagination.el).forEach((e=>{e.removeEventListener("keydown",f)}));getDocument().removeEventListener("visibilitychange",k),a.el&&"string"!=typeof a.el&&(a.el.removeEventListener("focus",M,!0),a.el.removeEventListener("pointerdown",h,!0),a.el.removeEventListener("pointerup",A,!0))}()}))}export{A11y as default};
//# sourceMappingURL=a11y.min.mjs.map