/** Classes added to HTML generated by  By default classnames follow the BEM notation. */
export interface ClassNames {
    /** @default ['choices'] */
    containerOuter: string | Array<string>;
    /** @default ['choices__inner'] */
    containerInner: string | Array<string>;
    /** @default ['choices__input'] */
    input: string | Array<string>;
    /** @default ['choices__input--cloned'] */
    inputCloned: string | Array<string>;
    /** @default ['choices__list'] */
    list: string | Array<string>;
    /** @default ['choices__list--multiple'] */
    listItems: string | Array<string>;
    /** @default ['choices__list--single'] */
    listSingle: string | Array<string>;
    /** @default ['choices__list--dropdown'] */
    listDropdown: string | Array<string>;
    /** @default ['choices__item'] */
    item: string | Array<string>;
    /** @default ['choices__item--selectable'] */
    itemSelectable: string | Array<string>;
    /** @default ['choices__item--disabled'] */
    itemDisabled: string | Array<string>;
    /** @default ['choices__item--choice'] */
    itemChoice: string | Array<string>;
    /** @default ['choices__description'] */
    description: string | Array<string>;
    /** @default ['choices__placeholder'] */
    placeholder: string | Array<string>;
    /** @default ['choices__group'] */
    group: string | Array<string>;
    /** @default ['choices__heading'] */
    groupHeading: string | Array<string>;
    /** @default ['choices__button'] */
    button: string | Array<string>;
    /** @default ['is-active'] */
    activeState: string | Array<string>;
    /** @default ['is-focused'] */
    focusState: string | Array<string>;
    /** @default ['is-open'] */
    openState: string | Array<string>;
    /** @default ['is-disabled'] */
    disabledState: string | Array<string>;
    /** @default ['is-highlighted'] */
    highlightedState: string | Array<string>;
    /** @default ['is-selected'] */
    selectedState: string | Array<string>;
    /** @default ['is-flipped'] */
    flippedState: string | Array<string>;
    /** @default ['is-loading'] */
    loadingState: string | Array<string>;
    /** @default ['choices__notice'] */
    notice: string | Array<string>;
    /** @default ['choices__item--selectable', 'add-choice'] */
    addChoice: string | Array<string>;
    /** @default ['has-no-results'] */
    noResults: string | Array<string>;
    /** @default ['has-no-choices'] */
    noChoices: string | Array<string>;
}
