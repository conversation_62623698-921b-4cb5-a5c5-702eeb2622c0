import Header from 'src/header';
import Tabular from '../tabular';
export declare const SetStatusToRendered: () => (state: any) => any;
export declare const SetLoadingData: () => (state: any) => any;
export declare const SetData: (data: Tabular) => (state: any) => any;
export declare const SetDataErrored: () => (state: any) => any;
export declare const SetHeader: (header: Header) => (state: any) => any;
export declare const SetTableRef: (tableRef: any) => (state: any) => any;
