[{"id": 12, "coverImg": "build/images/small/img-9.jpg", "bookmark": false, "memberImg": "build/images/users/avatar-2.jpg", "memberName": "<PERSON>", "position": "Team Leader & HR", "projects": "225", "tasks": "197"}, {"id": 11, "coverImg": "build/images/small/img-12.jpg", "bookmark": true, "memberImg": "", "nickname": "HB", "memberName": "<PERSON>", "position": "Full Stack Developer", "projects": "352", "tasks": "376"}, {"id": 10, "coverImg": "build/images/small/img-11.jpg", "bookmark": false, "memberImg": "build/images/users/avatar-3.jpg", "memberName": "<PERSON>", "position": "Project Manager", "projects": "164", "tasks": "182"}, {"id": 9, "coverImg": "build/images/small/img-1.jpg", "bookmark": true, "memberImg": "build/images/users/avatar-8.jpg", "memberName": "<PERSON>", "position": "UI/UX Designer", "projects": "241", "tasks": "204"}, {"id": 8, "coverImg": "build/images/small/img-10.jpg", "bookmark": false, "memberImg": "", "nickname": "ME", "memberName": "<PERSON>", "position": "Team Leader & Web Developer", "projects": "201", "tasks": "263"}, {"id": 7, "coverImg": "build/images/small/img-2.jpg", "bookmark": false, "memberImg": "build/images/users/avatar-4.jpg", "memberName": "<PERSON>", "position": "Backend Developer", "projects": "132", "tasks": "147"}, {"id": 6, "coverImg": "build/images/small/img-4.jpg", "bookmark": true, "memberImg": "", "nickname": "NC", "memberName": "<PERSON>", "position": "Front-End Developer", "projects": "352", "tasks": "376"}, {"id": 5, "coverImg": "build/images/small/img-7.jpg", "bookmark": true, "memberImg": "build/images/users/avatar-6.jpg", "memberName": "<PERSON>", "position": "Full Stack Developer", "projects": "64", "tasks": "93"}, {"id": 4, "coverImg": "build/images/small/img-3.jpg", "bookmark": false, "memberImg": "build/images/users/avatar-5.jpg", "memberName": "<PERSON>", "position": "Web Designer", "projects": "345", "tasks": "298"}, {"id": 3, "coverImg": "build/images/small/img-5.jpg", "bookmark": true, "memberImg": "", "nickname": "DP", "memberName": "<PERSON>", "position": "<PERSON><PERSON>", "projects": "97", "tasks": "135"}, {"id": 2, "coverImg": "build/images/small/img-8.jpg", "bookmark": false, "memberImg": "build/images/users/avatar-7.jpg", "memberName": "<PERSON>", "position": "React Js Developer", "projects": "87", "tasks": "121"}, {"id": 1, "coverImg": "build/images/small/img-6.jpg", "bookmark": false, "memberImg": "", "nickname": "MW", "memberName": "<PERSON>", "position": "Backend Developer", "projects": "145", "tasks": "210"}]