@extends('layouts.master-without-nav')

@section('title')
    404 Not Found
@endsection

@section('body')

    <body>
    @endsection

    @section('content')
        <!-- auth-page wrapper -->
        <div class="auth-page-wrapper auth-bg-cover py-5 d-flex justify-content-center align-items-center min-vh-100">
            <div class="bg-overlay" style="background-image: url(/build/images/desalpes-background-full.jpg);"></div>
            <!-- auth-page content -->
            <div class="auth-page-content overflow-hidden pt-lg-5">
                <div class="container">
                    <div class="row justify-content-center">
                        <div class="col-xl-7 col-lg-8">
                            <div class="card overflow-hidden">
                                <div class="card-body p-4">
                                    <div class="text-center">
                                        <a href="{{ url('/') }}" class="d-inline-block auth-logo mb-4">
                                            <img src="{{ URL::asset('build/images/des-alpes-logo.png') }}" alt=""
                                                height="40">
                                        </a>
                                        <img src="{{ URL::asset('build/images/error400-cover.png') }}" alt="error img"
                                            class="img-fluid">
                                        <div class="mt-4">
                                            <h3 class="text-uppercase">Sorry, Page not Found 😭</h3>
                                            <p class="text-muted mb-4">The page you are looking for is not available!</p>
                                            @auth
                                                @if (auth()->user()->hasRole('admin'))
                                                    <a href="{{ route('admin.dashboard') }}" class="btn btn-success"><i
                                                            class="mdi mdi-home me-1"></i>Back to Dashboard</a>
                                                @else
                                                    <a href="{{ route('user.dashboard') }}" class="btn btn-success"><i
                                                            class="mdi mdi-home me-1"></i>Back to Dashboard</a>
                                                @endif
                                            @else
                                                <a href="{{ route('login') }}" class="btn btn-success"><i
                                                        class="mdi mdi-home me-1"></i>Back to Login</a>
                                            @endauth
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div><!-- end col -->
                    </div>
                    <!-- end row -->
                </div>
                <!-- end container -->
            </div>
            <!-- end auth-page content -->
        </div>
        <!-- end auth-page-wrapper -->
    @endsection
