@extends('layouts.master')
@section('title')
    Commission Dashboard
@endsection
@section('css')
    <link href="{{ URL::asset('build/libs/jsvectormap/jsvectormap.min.css') }}" rel="stylesheet" type="text/css" />
    <link href="{{ URL::asset('build/libs/swiper/swiper-bundle.min.css') }}" rel="stylesheet" type="text/css" />
@endsection
@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Commission Dashboard</h4>
            </div>
        </div>
    </div>

    <!-- Commission Summary Cards -->
    <div class="row" id="commission-summary">
        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-uppercase fw-medium text-muted mb-0">Total Members</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                <span class="counter-value" data-target="0">0</span>
                            </h4>
                            <span class="badge bg-success-subtle text-success">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i> Active Members
                            </span>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-success-subtle rounded fs-3">
                                <i class="bx bx-user-circle text-success"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-uppercase fw-medium text-muted mb-0">AMB Commissions</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span class="counter-value"
                                    data-target="0">0</span></h4>
                            <span class="badge bg-info-subtle text-info">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i> Ambassador Program
                            </span>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-info-subtle rounded fs-3">
                                <i class="bx bx-award text-info"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-uppercase fw-medium text-muted mb-0">Revenue Commissions</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span class="counter-value"
                                    data-target="0">0</span></h4>
                            <span class="badge bg-warning-subtle text-warning">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i> Revenue Share
                            </span>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-warning-subtle rounded fs-3">
                                <i class="bx bx-dollar-circle text-warning"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-3 col-md-6">
            <div class="card card-animate">
                <div class="card-body">
                    <div class="d-flex align-items-center">
                        <div class="flex-grow-1">
                            <p class="text-uppercase fw-medium text-muted mb-0">To Be Paid</p>
                        </div>
                    </div>
                    <div class="d-flex align-items-end justify-content-between mt-4">
                        <div>
                            <h4 class="fs-22 fw-semibold ff-secondary mb-4">$<span class="counter-value"
                                    data-target="0">0</span></h4>
                            <span class="badge bg-danger-subtle text-danger">
                                <i class="ri-arrow-right-up-line fs-13 align-middle"></i> Pending Payouts
                            </span>
                        </div>
                        <div class="avatar-sm flex-shrink-0">
                            <span class="avatar-title bg-danger-subtle rounded fs-3">
                                <i class="bx bx-wallet text-danger"></i>
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Commission Trends Chart -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header border-0 align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Commission Trends</h4>
                    <div>
                        <button type="button" class="btn btn-soft-secondary btn-sm" data-period="all">
                            ALL
                        </button>
                        <button type="button" class="btn btn-soft-secondary btn-sm" data-period="1m">
                            1M
                        </button>
                        <button type="button" class="btn btn-soft-secondary btn-sm" data-period="6m">
                            6M
                        </button>
                        <button type="button" class="btn btn-soft-primary btn-sm" data-period="1y">
                            1Y
                        </button>
                    </div>
                </div>

                <div class="card-body p-0 pb-2">
                    <div class="w-100">
                        <div id="commission_trends_chart" class="apex-charts" dir="ltr"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Commission Distribution</h4>
                </div>

                <div class="card-body">
                    <div id="commission_distribution_chart" class="apex-charts" dir="ltr"></div>

                    <div class="px-2 py-2 mt-4">
                        <div class="commission-distribution-legend"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Top Earners Table -->
    <div class="row">
        <div class="col-xl-8">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Top Commission Earners</h4>
                    <div class="flex-shrink-0">
                        <button type="button" class="btn btn-soft-info btn-sm">
                            <i class="ri-file-list-3-line align-middle"></i> Export Report
                        </button>
                    </div>
                </div>

                <div class="card-body">
                    <div class="table-responsive table-card">
                        <table class="table table-hover table-centered align-middle table-nowrap mb-0"
                            id="topEarnersTable">
                            <thead class="text-muted table-light">
                                <tr>
                                    <th scope="col">Member</th>
                                    <th scope="col">AMB Level</th>
                                    <th scope="col">Total Earnings</th>
                                    <th scope="col">AMB Comm</th>
                                    <th scope="col">RV Comm</th>
                                    <th scope="col">RV Bonus</th>
                                </tr>
                            </thead>
                            <tbody></tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-xl-4">
            <div class="card">
                <div class="card-header align-items-center d-flex">
                    <h4 class="card-title mb-0 flex-grow-1">Payout Status</h4>
                </div>

                <div class="card-body">
                    <div id="payout_status_stats"></div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src="{{ URL::asset('build/libs/apexcharts/apexcharts.min.js') }}"></script>
    <script>
        // Commission Dashboard JavaScript
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize with data passed from controller
            const initialData = @json($data);
            updateCommissionSummary(initialData);

            // Initialize all widgets
            initializeCommissionTrends();
            initializeCommissionDistribution();
            initializeTopEarners();
            initializePayoutStatus();

            // Set up refresh intervals
            setInterval(refreshCommissionSummary, 300000); // 5 minutes
            setInterval(refreshCommissionTrends, 3600000); // 1 hour
            setInterval(refreshTopEarners, 900000); // 15 minutes
            setInterval(refreshPayoutStatus, 60000); // 1 minute
        });

        // Commission Summary Widget
        function refreshCommissionSummary() {
            fetch('{{ route('admin.dashboard') }}')
                .then(response => response.json())
                .then(data => updateCommissionSummary(data))
                .catch(error => console.error('Error fetching commission summary:', error));
        }

        function updateCommissionSummary(data) {
            // Update counter values
            document.querySelector('[data-target="total_members"]').setAttribute('data-target', data.total_members);
            document.querySelector('[data-target="amb_commissions"]').setAttribute('data-target', data.amb_commissions);
            document.querySelector('[data-target="rv_commissions"]').setAttribute('data-target', data.rv_commissions);
            document.querySelector('[data-target="to_be_paid"]').setAttribute('data-target', data.to_be_paid);
        }

        // Commission Trends Chart
        function initializeCommissionTrends() {
            fetch('{{ route('admin.dashboard.trends') }}')
                .then(response => response.json())
                .then(data => {
                    const options = {
                        series: [{
                            name: 'AMB Commission',
                            data: data.map(item => item.amb_comm)
                        }, {
                            name: 'RV Commission',
                            data: data.map(item => item.rv_comm)
                        }, {
                            name: 'RV Bonus',
                            data: data.map(item => item.rv_bonus)
                        }],
                        chart: {
                            type: 'line',
                            height: 350,
                            toolbar: {
                                show: false
                            }
                        },
                        stroke: {
                            curve: 'smooth',
                            width: 2
                        },
                        xaxis: {
                            categories: data.map(item => item.month)
                        },
                        tooltip: {
                            y: {
                                formatter: function(value) {
                                    return "$" + value.toFixed(2)
                                }
                            }
                        }
                    };

                    const chart = new ApexCharts(document.querySelector("#commission_trends_chart"), options);
                    chart.render();
                })
                .catch(error => console.error('Error fetching commission trends:', error));
        }

        // Commission Distribution Chart
        function initializeCommissionDistribution() {
            fetch('{{ route('admin.dashboard.distribution') }}')
                .then(response => response.json())
                .then(data => {
                    const options = {
                        series: data.map(item => item.total_comm),
                        chart: {
                            type: 'donut',
                            height: 300
                        },
                        labels: data.map(item => `AMB${item.amb_step}`),
                        legend: {
                            position: 'bottom'
                        },
                        tooltip: {
                            y: {
                                formatter: function(value) {
                                    return "$" + value.toFixed(2)
                                }
                            }
                        }
                    };

                    const chart = new ApexCharts(document.querySelector("#commission_distribution_chart"), options);
                    chart.render();

                    // Update legend with additional information
                    updateDistributionLegend(data);
                })
                .catch(error => console.error('Error fetching commission distribution:', error));
        }

        function updateDistributionLegend(data) {
            const legendContainer = document.querySelector('.commission-distribution-legend');
            legendContainer.innerHTML = data.map(item => `
                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-grow-1">
                        <p class="mb-0">AMB${item.amb_step}</p>
                                    </div>
                                    <div class="flex-shrink-0">
                        <h6 class="mb-0">$${item.total_comm.toFixed(2)}</h6>
                        <small class="text-muted">${item.member_count} members</small>
                                    </div>
                                </div>
            `).join('');
        }

        // Top Earners Table
        function initializeTopEarners() {
            fetch('{{ route('admin.dashboard.top-earners') }}')
                .then(response => response.json())
                .then(data => {
                    const tbody = document.querySelector('#topEarnersTable tbody');
                    tbody.innerHTML = data.map(earner => `
                        <tr>
                            <td>${earner.member.fname} ${earner.member.lname}</td>
                            <td>AMB${earner.amb_step}</td>
                            <td>$${earner.total_earnings.toFixed(2)}</td>
                            <td>$${earner.amb_comm.toFixed(2)}</td>
                            <td>$${earner.rv_comm.toFixed(2)}</td>
                            <td>$${earner.rv_bonus.toFixed(2)}</td>
                        </tr>
                    `).join('');
                })
                .catch(error => console.error('Error fetching top earners:', error));
        }

        // Payout Status Widget
        function initializePayoutStatus() {
            fetch('{{ route('admin.dashboard.payout-status') }}')
                .then(response => response.json())
                .then(data => {
                    const container = document.querySelector('#payout_status_stats');
                    container.innerHTML = `
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <p class="text-uppercase fw-medium text-muted mb-0">Pending</p>
                                <h4 class="fs-18 mb-0">$${(data.PENDING?.total_amount || 0).toFixed(2)}</h4>
                                <small class="text-muted">${data.PENDING?.count || 0} transactions</small>
                                        </div>
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-warning-subtle rounded fs-3">
                                    <i class="bx bx-time-five text-warning"></i>
                                </span>
                                    </div>
                                            </div>
                        <div class="d-flex align-items-center mb-3">
                            <div class="flex-grow-1">
                                <p class="text-uppercase fw-medium text-muted mb-0">Processing</p>
                                <h4 class="fs-18 mb-0">$${(data.PROCESSING?.total_amount || 0).toFixed(2)}</h4>
                                <small class="text-muted">${data.PROCESSING?.count || 0} transactions</small>
                                        </div>
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-info-subtle rounded fs-3">
                                    <i class="bx bx-loader-circle text-info"></i>
                                </span>
                                    </div>
                                        </div>
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1">
                                <p class="text-uppercase fw-medium text-muted mb-0">Completed</p>
                                <h4 class="fs-18 mb-0">$${(data.COMPLETED?.total_amount || 0).toFixed(2)}</h4>
                                <small class="text-muted">${data.COMPLETED?.count || 0} transactions</small>
                                    </div>
                            <div class="avatar-sm flex-shrink-0">
                                <span class="avatar-title bg-success-subtle rounded fs-3">
                                    <i class="bx bx-check-circle text-success"></i>
                                </span>
                                </div>
                                        </div>
                    `;
                })
                .catch(error => console.error('Error fetching payout status:', error));
        }

        // Refresh functions
        function refreshCommissionTrends() {
            initializeCommissionTrends();
        }

        function refreshTopEarners() {
            initializeTopEarners();
        }

        function refreshPayoutStatus() {
            initializePayoutStatus();
        }
    </script>
    <script src="{{ URL::asset('build/js/app.js') }}"></script>
@endsection
