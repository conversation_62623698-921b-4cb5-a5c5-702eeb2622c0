@extends('layouts.master')
@section('title')
    Members
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="page-title-box d-sm-flex align-items-center justify-content-between">
                <h4 class="mb-sm-0">Members</h4>
            </div>
        </div>

        {{-- Flash Messages --}}
        <div class="col-12 mb-4">
            @if (Session::has('success'))
                <div class="alert alert-success">{{ Session::get('success') }}</div>
            @endif
            @if (Session::has('error'))
                <div class="alert alert-danger">{{ Session::get('error') }}</div>
            @endif
        </div>
    </div>

    {{-- Stats Widgets --}}
    <div class="row">
        {{-- Total Stats Row --}}
        @foreach (['total', 'active', 'free', 'inactive'] as $key)
            <div class="col-xl-3 col-md-6">
                <div class="card card-animate">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 overflow-hidden">
                                <p class="text-uppercase fw-medium text-muted text-truncate mb-0">
                                    {{ $stats[$key]['label'] }}</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-end justify-content-between mt-4">
                            <div>
                                <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                    <span class="counter-value" data-target="{{ $stats[$key]['count'] }}">
                                        {{ number_format($stats[$key]['count']) }}
                                    </span>
                                </h4>
                                <p class="text-muted mb-0">
                                    <i class="{{ $stats[$key]['icon'] }} text-{{ $stats[$key]['color'] }} me-1"></i>
                                    {{ $stats[$key]['description'] }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    {{-- Ambassador Level Stats --}}
    <div class="row">
        @foreach (['amb1', 'amb2', 'amb3', 'amb4plus'] as $key)
            <div class="col-xl-3 col-md-6">
                <div class="card card-animate">
                    <div class="card-body">
                        <div class="d-flex align-items-center">
                            <div class="flex-grow-1 overflow-hidden">
                                <p class="text-uppercase fw-medium text-muted text-truncate mb-0">
                                    {{ $stats[$key]['label'] }}</p>
                            </div>
                        </div>
                        <div class="d-flex align-items-end justify-content-between mt-4">
                            <div>
                                <h4 class="fs-22 fw-semibold ff-secondary mb-4">
                                    <span class="counter-value" data-target="{{ $stats[$key]['count'] }}">
                                        {{ number_format($stats[$key]['count']) }}
                                    </span>
                                </h4>
                                <p class="text-muted mb-0">
                                    <i class="{{ $stats[$key]['icon'] }} text-{{ $stats[$key]['color'] }} me-1"></i>
                                    {{ $stats[$key]['description'] }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>

    <div class="row">
        <div class="col-xl-12">
            <div class="card">
                {{-- --}}{{-- Add Button Section --}}{{--
                <div class="card-header d-flex align-items-center">
                    <h5 class="card-title mb-0 flex-grow-1"></h5>
                    <div>
                        <a href="{{ route('admin.members.show', 'create') }}"
                            class="btn rounded-pill btn-primary waves-effect waves-light">
                            <i class="ri-add-fill ri-1x"></i> Add Member
                        </a>
                    </div>
                </div> --}}

                {{-- DataTable Section --}}
                <div class="card-body">
                    {!! $dataTable->table(['class' => 'table table-bordered dt-responsive nowrap w-100']) !!}
                </div>
            </div>
        </div>
    </div>

    {{-- Delete Confirmation Script --}}
    @push('scripts')
        {!! $dataTable->scripts() !!}
        <script>
            function deleteUser(id) {
                swal({
                    title: "Are you sure?",
                    text: "You won't be able to revert this!",
                    icon: "warning",
                    buttons: true,
                    dangerMode: true,
                }).then((willDelete) => {
                    if (willDelete) {
                        $.ajax({
                            type: 'post',
                            url: '{{ route('admin.member.delete') }}',
                            data: {
                                '_token': '{{ csrf_token() }}',
                                'id': id,
                            },
                            success: function(response) {
                                if (response.status === true) {
                                    swal({
                                        text: response.message,
                                        icon: "success",
                                    });
                                    $('#members-table').DataTable().draw();
                                } else {
                                    swal({
                                        text: response.message,
                                        icon: "warning",
                                    });
                                }
                                setTimeout(function() {
                                    swal.close();
                                }, 2000);
                            },
                            error: function(xhr) {
                                swal({
                                    text: xhr.responseJSON.message,
                                    icon: "warning",
                                });
                            }
                        });
                    }
                });
            }
        </script>
    @endpush
@endsection
